package vn.osp.common.domain.helpers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.msgpack.jackson.dataformat.MessagePackFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public final class TransformHelper {
    private TransformHelper() {
    }

    /**
     * Chuyển đổi chuỗi thành mảng byte sử dụng encoding UTF-8.
     *
     * @param content chuỗi cần chuyển đổi
     * @return mảng byte tương ứng, hoặc mảng rỗng nếu content null/empty
     */
    public static byte[] stringToByteArray(String content) {
        return EncodingHelper.stringToBytes(content, StandardCharsets.UTF_8);
    }

    /**
     * Chuyển đổi mảng byte thành chuỗi sử dụng encoding UTF-8.
     *
     * @param content mảng byte cần chuyển đổi
     * @return chuỗi tương ứng, hoặc chuỗi rỗng nếu content null/empty
     */
    public static String byteArrayToString(byte[] content) {
        return EncodingHelper.bytesToString(content, StandardCharsets.UTF_8);
    }

    /**
     * Chuyển đổi InputStream thành chuỗi Base64.
     *
     * @param content InputStream cần chuyển đổi
     * @return chuỗi Base64 hoặc chuỗi rỗng nếu stream là null
     * @throws IOException nếu đọc stream thất bại
     */
    public static String streamToBase64(InputStream content) throws IOException {
        if (content == null) return StringHelper.EMPTY;

        byte[] bytes = streamToByteArray(content);
        return EncodingHelper.base64Encode(bytes);
    }

    /**
     * Chuyển đổi InputStream thành mảng byte.
     *
     * @param stream InputStream cần chuyển đổi
     * @return mảng byte hoặc mảng rỗng nếu stream là null
     * @throws IOException nếu đọc stream thất bại
     */
    public static byte[] streamToByteArray(InputStream stream) throws IOException {
        if (stream == null) return new byte[0];

        byte[] buffer = new byte[16 * 1024]; // 16 KB buffer
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            int read;
            while ((read = stream.read(buffer)) != -1) {
                baos.write(buffer, 0, read);
            }
            return baos.toByteArray();
        }
    }

    /**
     * Chuyển đổi chuỗi Base64 thành InputStream.
     *
     * @param base64Content chuỗi Base64 cần chuyển đổi
     * @return InputStream hoặc null nếu input null/empty
     */
    public static InputStream base64ToStream(String base64Content) {
        if (StringHelper.isEmpty(base64Content)) return null;

        byte[] bytes = EncodingHelper.base64Decode(base64Content);
        return new ByteArrayInputStream(bytes);
    }

    /**
     * Chuyển đổi InputStream thành String sử dụng encoding UTF-8.
     *
     * @param stream InputStream cần chuyển đổi
     * @return chuỗi tương ứng hoặc chuỗi rỗng nếu stream là null
     * @throws IOException nếu đọc stream thất bại
     */
    public static String streamToString(InputStream stream) throws IOException {
        if (stream == null) return StringHelper.EMPTY;
        byte[] bytes = streamToByteArray(stream); // không đóng stream
        return new String(bytes, StandardCharsets.UTF_8);
    }

    /**
     * Chuyển đổi object thành chuỗi Base64.
     *
     * @param data Object cần chuyển đổi
     * @return chuỗi Base64 hoặc chuỗi rỗng nếu data là null
     * @throws JsonProcessingException nếu serialize object thất bại
     */
    public static String objectToBase64(Object data) throws JsonProcessingException {
        if (data == null) {
            return StringHelper.EMPTY;
        }

        // Serialize object → JSON → Base64
        String json = JsonHelper.toJson(data);
        return EncodingHelper.base64Encode(json);
    }

    /**
     * Chuyển đổi mảng byte thành InputStream.
     *
     * @param input mảng byte cần chuyển đổi
     * @return InputStream hoặc null nếu input null/empty
     */
    public static InputStream byteArrayToStream(byte[] input) {
        if (input == null || input.length == 0) {
            return null;
        }
        return new ByteArrayInputStream(input);
    }

    private static final ObjectMapper MSGPACK_MAPPER = new ObjectMapper(new MessagePackFactory());

    /**
     * Chuyển đổi object thành mảng byte sử dụng MessagePack format.
     *
     * @param obj object cần chuyển đổi
     * @param <T> kiểu dữ liệu của object
     * @return mảng byte, hoặc mảng rỗng nếu obj là null
     * @throws JsonProcessingException nếu serialize object thất bại
     */
    public static <T> byte[] toByteArray(T obj) throws JsonProcessingException {
        if (obj == null) return new byte[0];

        return MSGPACK_MAPPER.writeValueAsBytes(obj);
    }

    /**
     * Chuyển đổi mảng byte thành object sử dụng Class type.
     *
     * @param data  mảng byte cần chuyển đổi
     * @param clazz kiểu dữ liệu Class của T
     * @param <T>   kiểu dữ liệu của object kết quả
     * @return object kiểu T, hoặc null nếu data null/empty
     * @throws IOException nếu deserialize thất bại
     */
    public static <T> T fromByteArray(byte[] data, Class<T> clazz) throws IOException {
        if (data == null || data.length == 0) return null;

        return MSGPACK_MAPPER.readValue(data, clazz);
    }

    /**
     * Chuyển đổi mảng byte thành object sử dụng TypeReference.
     *
     * @param data    mảng byte cần chuyển đổi
     * @param typeRef kiểu dữ liệu TypeReference của T
     * @param <T>     kiểu dữ liệu của object kết quả
     * @return object kiểu T, hoặc null nếu data null/empty
     * @throws IOException nếu deserialize thất bại
     */
    public static <T> T fromByteArray(byte[] data, TypeReference<T> typeRef) throws IOException {
        if (data == null || data.length == 0) return null;

        return MSGPACK_MAPPER.readValue(data, typeRef);
    }

}
