<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout
                    pattern="%highlight{[%d{HH:mm:ss.SSS}] [%t] %-5level [%X{traceId:-} - %X{spanId:-}] %c{1.} - %msg%n%throwable}"/>
        </Console>
        <OpenTelemetry name="OpenTelemetryAppender"
                       captureMapMessageAttributes="true"
                       captureMarkerAttribute="true"
                       captureContextDataAttributes="userId,sessionId"
        />
    </Appenders>
    <Loggers>
        <Logger name="vn.osp.common.api.filters.RequestLoggingFilter" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="OpenTelemetryAppender"/>
        </Logger>
        <Root level="INFO">
            <AppenderRef ref="OpenTelemetryAppender"/>
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>