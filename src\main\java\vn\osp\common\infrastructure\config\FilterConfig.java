package vn.osp.common.infrastructure.config;

import io.micrometer.tracing.Tracer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import vn.osp.common.api.filters.ContextPropagationFilter;

@Configuration
public class FilterConfig {

    private final Tracer tracer;

    public FilterConfig(Tracer tracer) {
        this.tracer = tracer;
    }

    @Bean
    public FilterRegistrationBean<ContextPropagationFilter> contextPropagationFilter() {
        FilterRegistrationBean<ContextPropagationFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new ContextPropagationFilter(tracer));
        registration.addUrlPatterns("/*"); // áp dụng cho tất cả request
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 10); // thứ tự chạy
        return registration;
    }
}
