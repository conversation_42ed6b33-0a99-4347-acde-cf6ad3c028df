package vn.osp.common.api.filters;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.filter.AbstractRequestLoggingFilter;

import java.time.Instant;

public class RequestLoggingFilter extends AbstractRequestLoggingFilter {

    @Override
    protected boolean shouldLog(HttpServletRequest request) {
        return this.logger.isDebugEnabled();
    }

    @Override
    protected void beforeRequest(HttpServletRequest request, String message) {
        // Ghi lại start time vào attribute của request
        request.setAttribute("startTime", Instant.now().toEpochMilli());

        this.logger.debug("Incoming request: [%s]".formatted(message));
    }

    @Override
    protected void afterRequest(HttpServletRequest request, String message) {
        long startTime = (long) request.getAttribute("startTime");
        long duration = Instant.now().toEpochMilli() - startTime;

        this.logger.debug("Completed request: [%s] | Response time = %d ms".formatted(message, duration));
    }
}
