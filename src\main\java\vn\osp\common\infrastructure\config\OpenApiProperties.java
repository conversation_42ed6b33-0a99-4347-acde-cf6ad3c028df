package vn.osp.common.infrastructure.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@Getter
@Setter
@ConfigurationProperties(prefix = "openapi")
public class OpenApiProperties {

    private String title = "API Documentation";
    private String version = "1.0.0";
    private String description = "Auto-configured OpenAPI";

    private boolean enableBearer = false;
    private boolean enableBasic = false;
    private boolean enableApiKey = false;
    private boolean enableOAuth2 = false;

    // OAuth2 properties gộp vào đây
    private OAuth2Properties oauth2 = new OAuth2Properties();


    // Inner class cho OAuth2
    @Getter
    @Setter
    public static class OAuth2Properties {
        private String clientId;
        private String clientSecret;
        private String flow = "authorizationCode";
        private String authorizationUrl;
        private String tokenUrl;
        private Map<String, String> scopes;
    }
}


