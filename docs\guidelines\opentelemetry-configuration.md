# Hướng dẫn C<PERSON><PERSON> hình OpenTelemetry trong Spring Boot

## Tổng quan

Tài liệu này mô tả cách cấu hình và sử dụng **OpenTelemetry** cho việc thu thập **Metrics**, **Logging** và **Tracing** trong ứng dụng Spring Boot. Hệ thống được thiết kế để gửi dữ liệu telemetry tới OpenTelemetry Collector thông qua giao thức OTLP (OpenTelemetry Protocol).

## Kiến trúc Hệ thống

```
Application → OpenTelemetry SDK (in-process) → OTLP Exporter → Collector (localhost:4317/4318)
```

### Thành phần chính:
- **Micrometer Tracing**: Bridge giữa Spring Boot và OpenTelemetry
- **OpenTelemetry SDK**: Core library cho instrumentation
- **OTLP Exporter**: G<PERSON>i dữ liệu tới Collector
- **Log4j2 OpenTelemetry Appender**: <PERSON><PERSON><PERSON> hợ<PERSON> logging với tracing

## 1. Dependencies trong pom.xml

### 1.1. Version Management
```xml
<properties>
    <opentelemetry.version>1.49.0</opentelemetry.version>
    <micrometer.version>1.15.3</micrometer.version>
    <micrometer-tracing.version>1.5.3</micrometer-tracing.version>
    <opentelemetry-instrumentation.version>2.19.0-alpha</opentelemetry-instrumentation.version>
</properties>
```

### 1.2. Core Dependencies
```xml
<!-- Micrometer Tracing Bridge -->
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-tracing-bridge-otel</artifactId>
</dependency>

<!-- OTLP Exporter -->
<dependency>
    <groupId>io.opentelemetry</groupId>
    <artifactId>opentelemetry-exporter-otlp</artifactId>
</dependency>

<!-- Metrics Registry -->
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-registry-otlp</artifactId>
</dependency>

<!-- Logging Integration -->
<dependency>
    <groupId>io.opentelemetry.instrumentation</groupId>
    <artifactId>opentelemetry-log4j-appender-2.17</artifactId>
    <version>${opentelemetry-instrumentation.version}</version>
</dependency>

<!-- Spring Boot Actuator -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>

## 2. Cấu hình application.yml

### 2.1. Service Identification
```yaml
management:
  opentelemetry:
    resource-attributes:
      service.name: my-spring-service          # Tên service
      service.namespace: common                # Namespace/team
      service.version: v1.0.0                 # Version
      deployment.environment: dev             # Environment
```

### 2.2. Tracing Configuration
```yaml
management:
  tracing:
    enabled: true                             # Bật tracing
    propagation:
      consume: w3c                            # Nhận trace context theo W3C
      produce: w3c                            # Tạo trace context theo W3C
      type: w3c

    sampling:
      probability: 1.0                        # 100% sampling (dev), 0.1 cho production

    baggage:
      enabled: true                           # Bật baggage propagation
      correlation:
        fields:                               # Truyền vào MDC
          - userId
          - sessionId
      tag-fields:                             # Thêm vào span tags
          - userId
          - sessionId
```

### 2.3. OTLP Exporter Configuration
```yaml
management:
  otlp:
    logging:
      endpoint: http://localhost:4317         # gRPC endpoint cho logs
      transport: grpc
      compression: gzip
    
    tracing:
      endpoint: http://localhost:4317         # gRPC endpoint cho traces
      transport: grpc
      compression: gzip
    
    metrics:
      export:
        enabled: true
        url: http://localhost:4318/v1/metrics # HTTP endpoint cho metrics
        step: 1m                              # Export interval
        batch-size: 10000                     # Batch size
```

### 2.4. Actuator Endpoints
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health, metrics              # Expose health và metrics
```

## 3. Logging Configuration (log4j2-spring.xml)

### 3.1. Console Appender với Trace Context
```xml
<Console name="Console" target="SYSTEM_OUT">
    <PatternLayout
        pattern="%highlight{[%d{HH:mm:ss.SSS}] [%t] %-5level [%X{traceId:-} - %X{spanId:-}] %c{1.} - %msg%n%throwable}"/>
</Console>
```

### 3.2. OpenTelemetry Appender
```xml
<OpenTelemetry name="OpenTelemetryAppender"
               captureMapMessageAttributes="true"
               captureMarkerAttribute="true"
               captureContextDataAttributes="userId,sessionId" />
```

**Thuộc tính:**
- `captureMapMessageAttributes`: Thu thập structured logging attributes
- `captureMarkerAttribute`: Thu thập SLF4J markers
- `captureContextDataAttributes`: Thu thập MDC fields cụ thể

### 3.3. Logger Configuration
```xml
<Loggers>
    <!-- Specific logger với DEBUG level -->
    <Logger name="vn.osp.common.api.filters.RequestLoggingFilter" level="DEBUG" additivity="false">
        <AppenderRef ref="Console"/>
        <AppenderRef ref="OpenTelemetryAppender"/>
    </Logger>
    
    <!-- Root logger -->
    <Root level="INFO">
        <AppenderRef ref="OpenTelemetryAppender"/>
        <AppenderRef ref="Console"/>
    </Root>
</Loggers>
```

## 4. Java Configuration

### 4.1. Main Application Class Configuration

**Mục đích**: Cấu hình chính cho Spring Boot application với OpenTelemetry support và logging bridge.

```java
@SpringBootApplication(scanBasePackages = {
        "vn.osp.democommon",   // package chính của ứng dụng
        "vn.osp.common"        // package common-lib (OSP Common Library)
})
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class DemoCommonApplication {

    public static void main(String[] args) {
        // Remove default handlers attached to JUL (Java Util Logging)
        LogManager.getLogManager().reset();
        
        // Install SLF4J bridge để redirect JUL logs sang SLF4J/Log4j2
        SLF4JBridgeHandler.install();
        
        SpringApplication.run(DemoCommonApplication.class, args);
    }
}
```

**Giải thích chi tiết các annotations và cấu hình:**

#### 4.1.1. @SpringBootApplication Configuration
```java
@SpringBootApplication(scanBasePackages = {
    "vn.osp.democommon",   // Main application package
    "vn.osp.common"        // Common library package
})
```

**Tại sao cần scanBasePackages:**
- **Default behavior**: Spring Boot chỉ scan package của main class và sub-packages
- **Common library integration**: `vn.osp.common` chứa các components cần được Spring quản lý:
  - `@Component` classes (Aspects, Filters, Configurations)
  - `@Configuration` classes (OpenTelemetry configs)
  - `@RestControllerAdvice` (GlobalExceptionHandler)
- **Package separation**: Tách biệt business logic và infrastructure concerns

**Structure example:**
```
vn.osp.democommon/          # Main application
├── controller/
├── service/
├── repository/
└── DemoCommonApplication.java

vn.osp.common/              # Common library
├── api/
│   ├── exceptionhandlers/  # GlobalExceptionHandler
│   └── filters/           # ContextPropagationFilter, RequestLoggingFilter
├── infrastructure/
│   ├── aspects/           # ExceptionLoggingAspect, ExecutionTimeAspect
│   ├── config/            # FilterConfig, OpenTelemetryAppenderInitializer
│   └── annotations/       # @LogExecutionTime, @Auditable
```

#### 4.1.2. @EnableAspectJAutoProxy Configuration
```java
@EnableAspectJAutoProxy(proxyTargetClass = true)
```

**Chức năng:**
- **Enable AOP**: Kích hoạt Aspect-Oriented Programming support
- **proxyTargetClass = true**: Sử dụng CGLIB proxies thay vì JDK dynamic proxies

**So sánh proxy types:**
```java
// JDK Dynamic Proxy (proxyTargetClass = false)
// - Chỉ hoạt động với interfaces
// - Faster creation, slower execution
interface UserService {
    User findById(Long id);
}

// CGLIB Proxy (proxyTargetClass = true) 
// - Hoạt động với concrete classes
// - Slower creation, faster execution
@Service
public class UserServiceImpl implements UserService {
    @LogExecutionTime
    public User findById(Long id) { ... }
}
```

**Lý do chọn CGLIB:**
- Aspects có thể áp dụng cho concrete classes (không cần interface)
- Better performance cho method execution
- Tương thích với Spring Boot defaults

#### 4.1.3. Java Util Logging (JUL) Bridge Setup

**Problem**: Một số thư viện Java sử dụng JUL thay vì SLF4J, gây ra:
- Log messages bị mất
- Không tích hợp với OpenTelemetry
- Inconsistent log formatting

**Solution**: Redirect JUL logs sang SLF4J/Log4j2

```java
public static void main(String[] args) {
    // Step 1: Reset JUL configuration
    LogManager.getLogManager().reset();
    
    // Step 2: Install SLF4J bridge
    SLF4JBridgeHandler.install();
    
    // Step 3: Start Spring Boot
    SpringApplication.run(DemoCommonApplication.class, args);
}
```

**Detailed explanation:**

##### Step 1: Reset JUL Configuration
```java
LogManager.getLogManager().reset();
```
- **Purpose**: Xóa tất cả default JUL handlers (ConsoleHandler, FileHandler)
- **Effect**: Prevent duplicate logs và conflicts
- **Timing**: Phải gọi trước `SLF4JBridgeHandler.install()`

##### Step 2: Install SLF4J Bridge
```java
SLF4JBridgeHandler.install();
```
- **Purpose**: Route JUL log records tới SLF4J framework
- **Mechanism**: Thay thế JUL handlers bằng SLF4JBridgeHandler
- **Benefit**: JUL logs sẽ đi qua Log4j2 và OpenTelemetry Appender

**Dependency requirement trong pom.xml:**
```xml
<dependency>
    <groupId>org.slf4j</groupId>
    <artifactId>jul-to-slf4j</artifactId>
    <version>2.0.17</version>
</dependency>
```

#### 4.1.4. Alternative Configuration với Programmatic Setup

**Advanced setup cho complex scenarios:**
```java
@SpringBootApplication(scanBasePackages = {
    "vn.osp.democommon",
    "vn.osp.common"
})
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class DemoCommonApplication {

    public static void main(String[] args) {
        // JUL Bridge setup
        setupJavaUtilLoggingBridge();
        
        // Custom Spring Application setup
        SpringApplication app = new SpringApplication(DemoCommonApplication.class);
        
        // Set additional properties
        app.setAdditionalProfiles("otel"); // Activate OpenTelemetry profile
        
        // Run application
        app.run(args);
    }
    
    private static void setupJavaUtilLoggingBridge() {
        // Check if bridge is already installed
        if (!SLF4JBridgeHandler.isInstalled()) {
            LogManager.getLogManager().reset();
            SLF4JBridgeHandler.install();
        }
    }
}
```

#### 4.1.5. Configuration Validation

**Verify setup đang hoạt động đúng:**
```java
@Component
public class ConfigurationValidator implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationValidator.class);
    
    @Override
    public void run(ApplicationArguments args) {
        // Test SLF4J bridge
        java.util.logging.Logger julLogger = java.util.logging.Logger.getLogger("TestJUL");
        julLogger.info("This JUL message should appear in Log4j2");
        
        // Test Log4j2
        logger.info("This Log4j2 message should have trace context");
        
        // Test AspectJ
        logger.info("AspectJ proxy support: {}", 
                   AopUtils.isAopProxy(this) ? "ENABLED" : "DISABLED");
    }
}
```

#### 4.1.6. Common Issues và Solutions

**Issue 1: ClassNotFoundException: SLF4JBridgeHandler**
```xml
<!-- Missing dependency -->
<dependency>
    <groupId>org.slf4j</groupId>
    <artifactId>jul-to-slf4j</artifactId>
</dependency>
```

**Issue 2: Duplicate logs**
```java
// Wrong order - call reset() first
LogManager.getLogManager().reset();  // Must be first
SLF4JBridgeHandler.install();        // Then install bridge
```

**Issue 3: Aspects không hoạt động**
```java
// Missing annotation
@EnableAspectJAutoProxy(proxyTargetClass = true)  // Required

// Wrong package scan
@SpringBootApplication(scanBasePackages = {
    "vn.osp.common"  // Must include common library package
})
```

**Issue 4: Performance impact**
```java
// JUL bridge có overhead, chỉ dùng khi cần thiết
// Alternative: Configure JUL to use Log4j2 directly
```

#### 4.1.7. Best Practices

**✅ Recommended practices:**
```java
// 1. Always reset JUL before installing bridge
LogManager.getLogManager().reset();
SLF4JBridgeHandler.install();

// 2. Use specific package scanning
@SpringBootApplication(scanBasePackages = {
    "com.company.app",      // Application packages
    "vn.osp.common"         // Library packages only
})

// 3. Enable CGLIB proxies for better AOP support
@EnableAspectJAutoProxy(proxyTargetClass = true)
```

**❌ Avoid these patterns:**
```java
// 1. Don't use default package scanning với large libraries
@SpringBootApplication  // Will scan everything under main class package

// 2. Don't install JUL bridge multiple times
if (!SLF4JBridgeHandler.isInstalled()) {
    SLF4JBridgeHandler.install();
}

// 3. Don't mix logging frameworks without bridges
// Use either JUL→SLF4J→Log4j2 OR pure Log4j2
```

### 4.2. OpenTelemetry Appender Initializer
```java
@Component
@RequiredArgsConstructor
class OpenTelemetryAppenderInitializer implements InitializingBean {
    private final OpenTelemetry openTelemetry;

    @Override
    public void afterPropertiesSet() {
        OpenTelemetryAppender.install(this.openTelemetry);
    }
}
```

**Chức năng:** Khởi tạo OpenTelemetry Appender với OpenTelemetry instance từ Spring context.

### 4.3. Context Propagation Filter
```java
@Configuration
public class FilterConfig {
    private final Tracer tracer;

    @Bean
    public FilterRegistrationBean<ContextPropagationFilter> contextPropagationFilter() {
        FilterRegistrationBean<ContextPropagationFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new ContextPropagationFilter(tracer));
        registration.addUrlPatterns("/*");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 10);
        return registration;
    }
}
```

**Filter Logic:**
```java
public class ContextPropagationFilter extends OncePerRequestFilter {
    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
      String userId = Optional.ofNullable(this.tracer.getBaggage("userId"))
              .map(Baggage::get)
              .orElse(null);
      String sessionId = Optional.ofNullable(this.tracer.getBaggage("sessionId"))
              .map(Baggage::get)
              .orElse(null);

        // Generate userId và sessionId nếu chưa có
        if (userId == null || userId.isEmpty()) {
            userId = UUID.randomUUID().toString(); // TODO: Thay bằng Spring Security
        }
        if (sessionId == null || sessionId.isEmpty()) {
          HttpSession session = request.getSession(false); // không tạo mới
          sessionId = (session != null) ? session.getId() : null;
        }

        // Propagate baggage trong request scope
        try (BaggageInScope scope1 = this.tracer.createBaggageInScope("userId", userId);
             BaggageInScope scope2 = this.tracer.createBaggageInScope("sessionId", sessionId)) {
            filterChain.doFilter(request, response);
        }
    }
}
```

### 4.4. Request Logging Filter
```java
@Configuration
public class RequestLoggingFilterConfig {
    @Bean
    public RequestLoggingFilter logFilter() {
        RequestLoggingFilter filter = new RequestLoggingFilter();
        filter.setIncludeQueryString(true);        // Log query parameters
        filter.setIncludePayload(true);            // Log request body
        filter.setMaxPayloadLength(100);           // Giới hạn payload size
        filter.setIncludeHeaders(false);           // Không log headers (security)
        return filter;
    }
}
```

## 5. Instrumentation và Aspects

**Instrumentation** là quá trình thêm mã theo dõi (monitoring code) vào ứng dụng để thu thập dữ liệu telemetry. Trong Spring Boot, chúng ta sử dụng **Aspect-Oriented Programming (AOP)** để thực hiện cross-cutting concerns như logging, performance monitoring, và exception tracking một cách tự động và không xâm phạm (non-intrusive).

### 5.1. Exception Logging Aspect

**Mục đích**: Tự động ghi log tất cả exceptions xảy ra trong các layer chính của ứng dụng (Controller, Service, Repository).

```java
@Aspect
@Component
@Slf4j
public class ExceptionLoggingAspect {
    // Pointcut cho Service layer
    @Pointcut("execution(public * *(..)) && within(@org.springframework.stereotype.Service *)")
    public void serviceMethods() {}

    // Pointcut cho Repository layer  
    @Pointcut("execution(public * *(..)) && within(@org.springframework.stereotype.Repository *)")
    public void repositoryMethods() {}

    // Pointcut cho Controller layer
    @Pointcut("execution(public * *(..)) && within(@org.springframework.web.bind.annotation.RestController *)")
    public void controllerMethods() {}

    // Advice chạy khi có exception trong các methods được target
    @AfterThrowing(pointcut = "serviceMethods() || repositoryMethods() || controllerMethods()", throwing = "ex")
    public void logException(JoinPoint joinPoint, Throwable ex) {
        log.error("[ERROR] Exception in {}.{}() with cause={}", 
                 joinPoint.getSignature().getDeclaringType().getSimpleName(), 
                 joinPoint.getSignature().getName(), 
                 ex.getMessage(), ex);
    }
}
```

**Giải thích chi tiết:**

- **@Pointcut**: Định nghĩa các điểm cắt (join points) - nơi aspect sẽ được áp dụng
  - `execution(public * *(..))`: Khớp với tất cả public methods
  - `within(@Service *)`: Chỉ áp dụng trong classes có annotation @Service
  - `&&`: Operator AND để kết hợp điều kiện

- **@AfterThrowing**: Advice chạy sau khi method throw exception
  - `pointcut`: Kết hợp các pointcuts bằng operator OR (`||`)
  - `throwing = "ex"`: Bind exception object vào parameter `ex`

- **JoinPoint**: Cung cấp thông tin về method đang được intercept
  - `getSignature()`: Lấy method signature
  - `getDeclaringType()`: Lấy class chứa method

**Lợi ích:**
- Centralized exception logging cho toàn bộ ứng dụng
- Tự động capture stack trace và context
- Tích hợp với OpenTelemetry để correlation với traces
- Không cần modify từng method để thêm exception handling

### 5.2. Execution Time Aspect

**Mục đích**: Đo thời gian thực thi của các methods quan trọng để phát hiện performance bottlenecks.

```java
@Aspect
@Component
@Slf4j
public class ExecutionTimeAspect {
    // Around advice cho methods có @LogExecutionTime annotation
    @Around("@annotation(vn.osp.common.infrastructure.annotations.LogExecutionTime)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();

        Object result = joinPoint.proceed();  // Thực thi method gốc

        long duration = System.currentTimeMillis() - start;
        
        log.info("[PERF] {}.{}() executed in {} ms", 
                joinPoint.getSignature().getDeclaringType().getSimpleName(), 
                joinPoint.getSignature().getName(), 
                duration);
        return result;
    }
}
```

**Giải thích chi tiết:**

- **@Around**: Advice chạy xung quanh method execution
  - Có thể control việc có proceed với method gốc hay không
  - Có thể modify parameters hoặc return value

- **@annotation()**: Pointcut khớp với methods có annotation cụ thể
  - Chỉ áp dụng cho methods được đánh dấu `@LogExecutionTime`
  - Selective instrumentation thay vì apply cho tất cả methods

- **ProceedingJoinPoint**: Special join point cho @Around advice
  - `proceed()`: Thực thi method gốc và lấy kết quả
  - Bắt buộc phải call `proceed()` để method gốc được chạy

- **Performance Measurement**:
  - `System.currentTimeMillis()`: Đo thời gian với độ chính xác millisecond
  - Tính toán duration = end_time - start_time
  - Log với prefix `[PERF]` để dễ filter

**Usage Example:**
```java
@Service
public class UserService {
    @LogExecutionTime  // Chỉ method này được đo performance
    public User findById(Long id) {
        // Business logic
        return userRepository.findById(id);
    }
    
    public List<User> findAll() {
        // Method này không được đo performance
        return userRepository.findAll();
    }
}
```

**Custom Annotation:**
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogExecutionTime {
}
```

### 5.3. Global Exception Handler Integration

**Mục đích**: Tích hợp exception handling với OpenTelemetry tracing để correlation errors với traces.

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    private final Tracer tracer;

    // Record exception vào current OpenTelemetry span
    private void recordException(Exception ex) {
        Span span = tracer.currentSpan();
        if (span != null) {
            span.error(ex);  // Ghi lỗi vào current span
        }
    }

    // Enrich response với correlation information
    private void enrichCommon(ProblemDetail problem) {
        String correlationId = MDC.get("traceId");
        problem.setProperty("correlationId", correlationId);
        problem.setProperty("timestamp", OffsetDateTime.now());
    }
}
```

**Giải thích chi tiết:**

- **Tracer Integration**:
  - `tracer.currentSpan()`: Lấy span hiện tại trong context
  - `span.error(ex)`: Đánh dấu span có lỗi và attach exception details
  - Exception sẽ được hiển thị trong tracing UI (Jaeger, Zipkin)

- **Correlation ID**:
  - `MDC.get("traceId")`: Lấy trace ID từ Mapped Diagnostic Context
  - Thêm vào response để client có thể trace lỗi
  - Liên kết giữa logs, traces, và API responses

- **RFC 7807 Problem Details**:
  - `ProblemDetail`: Standard format cho API error responses
  - `setProperty()`: Thêm custom metadata vào response
  - Structured error format giúp client xử lý lỗi dễ dàng hơn

### 5.4. Audit Aspect (Bonus)

**Mục đích**: Tracking user actions cho security và compliance.

```java
@Aspect
@Component
@Slf4j
public class AuditAspect {
    @Around("@annotation(vn.osp.common.infrastructure.annotations.Auditable)")
    public Object auditMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        String userId = MDC.get("userId");
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getSignature().getDeclaringType().getSimpleName();
        
        log.info("[AUDIT] User {} called {}.{}()", userId, className, methodName);
        
        try {
            Object result = joinPoint.proceed();
            log.info("[AUDIT] User {} successfully completed {}.{}()", userId, className, methodName);
            return result;
        } catch (Exception ex) {
            log.warn("[AUDIT] User {} failed to complete {}.{}() due to: {}", 
                    userId, className, methodName, ex.getMessage());
            throw ex;
        }
    }
}
```

### 5.5. Best Practices cho Aspects

#### 5.5.1. Performance Considerations
```java
// ❌ Bad: Quá broad, impact performance
@Pointcut("execution(* *(..))")  // Khớp với TẤT CẢ methods

// ✅ Good: Selective targeting
@Pointcut("@annotation(LogExecutionTime)")  // Chỉ methods được đánh dấu
@Pointcut("within(@Service *)")  // Chỉ Service classes
```

#### 5.5.2. Exception Handling trong Aspects
```java
@Around("businessMethods()")
public Object handleBusinessLogic(ProceedingJoinPoint joinPoint) throws Throwable {
    try {
        return joinPoint.proceed();
    } catch (BusinessException ex) {
        // Log business exceptions differently
        log.warn("[BUSINESS] {}: {}", joinPoint.getSignature().getName(), ex.getMessage());
        throw ex;
    } catch (Exception ex) {
        // Log technical exceptions
        log.error("[TECH] Unexpected error in {}", joinPoint.getSignature().getName(), ex);
        throw ex;
    }
}
```

#### 5.5.3. Conditional Execution
```java
@Before("controllerMethods()")
public void beforeController(JoinPoint joinPoint) {
    // Chỉ log trong development environment
    if (environment.acceptsProfiles(Profiles.of("dev"))) {
        log.debug("Entering {}.{}()", 
                 joinPoint.getSignature().getDeclaringType().getSimpleName(),
                 joinPoint.getSignature().getName());
    }
}
```

### 5.6. Aspect Order và Dependencies

**Thứ tự thực thi quan trọng:**
```java
@Aspect
@Order(1)  // Chạy đầu tiên
public class SecurityAspect { }

@Aspect
@Order(2)  // Chạy sau Security
public class AuditAspect { }

@Aspect
@Order(3)  // Chạy cuối cùng
public class PerformanceAspect { }
```

**Auto-configuration:**
```java
@Configuration
@EnableAspectJAutoProxy  // Bật AOP support
public class AspectConfig {
    // Các aspect sẽ được Spring tự động detect và register
}
```

**Lợi ích tổng thể của Aspects:**
- **Separation of Concerns**: Tách biệt business logic và cross-cutting concerns
- **Code Reusability**: Một aspect có thể áp dụng cho nhiều classes/methods
- **Maintainability**: Dễ dàng modify hoặc disable instrumentation
- **Non-intrusive**: Không cần thay đổi business code
- **Centralized Configuration**: Tất cả concerns được quản lý ở một nơi

## 6. Data Flow và Correlation

### 6.1. Trace Context Propagation
```
HTTP Request → ContextPropagationFilter → Controller → Service → Repository
     ↓              ↓                        ↓          ↓         ↓
   TraceId      Baggage Creation         Span Creation            Span Creation
   SpanId       (userId, sessionId)      Auto-instrumentation    Manual spans
```

### 6.2. Logging Correlation
```
Log Pattern: [timestamp] [thread] LEVEL [traceId - spanId] class - message
Example: [10:30:45.123] [http-nio-8080-exec-1] INFO [64a8f2b1c3d4e5f6 - 789abc123def456] RequestController - Processing request
```

### 6.3. Baggage Propagation
- **userId**: Identifier của user (từ JWT hoặc session)
- **sessionId**: HTTP session ID
- Được truyền qua tất cả services và ghi vào logs/metrics

## 7. Collector Configuration

### 7.1. Docker Compose Example
```yaml
version: '3.8'
services:
  otel-collector:
    image: otel/opentelemetry-collector-contrib:latest
    ports:
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
    volumes:
      - ./otel-config.yaml:/etc/otel-collector-config.yaml
    command: ["--config=/etc/otel-collector-config.yaml"]
```

### 7.2. Collector Config (otel-config.yaml)
```yaml
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

processors:
  batch:

exporters:
  logging:
    loglevel: debug
  # Thêm các exporter khác (Jaeger, Prometheus, etc.)

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [logging]
    metrics:
      receivers: [otlp]
      processors: [batch]
      exporters: [logging]
    logs:
      receivers: [otlp]
      processors: [batch]
      exporters: [logging]
```

## 8. Best Practices

### 8.1. Sampling Strategy
- **Development**: `probability: 1.0` (100% tracing)
- **Production**: `probability: 0.1` (10% tracing để giảm overhead)

### 8.2. Security Considerations
- Không log sensitive data trong baggage
- Sử dụng `setIncludeHeaders(false)` trong RequestLoggingFilter
- Hide details trong production environment

### 8.3. Performance Tuning
- Sử dụng batch export với `batch-size: 10000`
- Cấu hình `step: 1m` cho metrics export
- Sử dụng gRPC thay vì HTTP cho better performance

### 8.4. Monitoring
- Monitor collector health qua port 4317/4318
- Check actuator endpoints: `/actuator/health`, `/actuator/metrics`
- Verify trace propagation qua logs

## 9. Troubleshooting

### 9.1. Common Issues
- **No traces**: Kiểm tra collector endpoint và network connectivity
- **Missing correlation**: Verify ContextPropagationFilter order
- **Performance issues**: Giảm sampling probability

### 9.2. Debug Commands
```bash
# Kiểm tra metrics
curl http://localhost:8080/actuator/metrics

# Kiểm tra health
curl http://localhost:8080/actuator/health

# Test collector connectivity
telnet localhost 4317
```

## 10. Migration Guide

### 10.1. Từ Logback sang Log4j2
1. Exclude `spring-boot-starter-logging`
2. Add `spring-boot-starter-log4j2`
3. Migrate `logback-spring.xml` sang `log4j2-spring.xml`

### 10.2. Enable OpenTelemetry
1. Add dependencies vào `pom.xml`
2. Cấu hình `application.yml`
3. Setup Log4j2 appender
4. Create configuration classes

## Tags
`opentelemetry`, `observability`, `tracing`, `metrics`, `logging`, `spring-boot`, `micrometer`, `otlp`, `log4j2`, `correlation`
