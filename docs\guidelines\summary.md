# Guidelines Summary

<PERSON>h sách các hướng dẫn kỹ thuật của dự án OSP Common Backend Java.

## Danh sách Guidelines

| Tên Guidelines | Mô tả tóm tắt | Tags |
|---------------|---------------|------|
| GlobalExceptionHandler | Tài liệu chi tiết về xử lý exception tập trung trong Spring Boot sử dụng @RestControllerAdvice, RFC 7807 Problem Details, và Clean Architecture | `spring-boot`, `exception-handling`, `clean-architecture`, `rfc-7807`, `api-layer`, `error-management` |
| OpenTelemetry Configuration | Hướng dẫn cấu hình và sử dụng OpenTelemetry cho việc thu thập Metrics, Logging và Tracing trong Spring Boot với OTLP protocol | `opentelemetry`, `observability`, `tracing`, `metrics`, `logging`, `spring-boot`, `micrometer`, `otlp`, `log4j2`, `correlation` |

## Quy tắc quản lý

1. **Tạo mới**: Khi thêm guideline mới, tạo file markdown trong thư mục `/docs/guidelines/`
2. **Cập nhật**: Thêm entry mới vào bảng trên với đầy đủ thông tin
3. **Tags**: Sử dụng tags để phân loại và tìm kiếm dễ dàng
4. **Naming**: Sử dụng kebab-case cho tên file (ví dụ: `global-exception-handler.md`)
