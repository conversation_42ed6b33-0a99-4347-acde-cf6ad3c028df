package vn.osp.common.infrastructure.aspects;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class AuditAspect {
    @After("@annotation(vn.osp.common.infrastructure.annotations.Auditable)")
    public void auditAction(JoinPoint joinPoint) {
        log.info("[AUDIT] {}.{}() executed",
                 joinPoint.getSignature().getDeclaringType().getSimpleName(),
                 joinPoint.getSignature().getName());
        // <PERSON>ó thể mở rộng: l<PERSON>u vào DB, gửi event sang Kafka, push log sang ELK/SigNoz...
    }
}
