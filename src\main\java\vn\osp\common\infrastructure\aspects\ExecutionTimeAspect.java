package vn.osp.common.infrastructure.aspects;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Aspect
@Component
@Slf4j
public class ExecutionTimeAspect {
    @Around("@annotation(vn.osp.common.infrastructure.annotations.LogExecutionTime)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = Instant.now().toEpochMilli();

        try {
            return joinPoint.proceed();
        } finally {
            long duration = Instant.now().toEpochMilli() - start;
            log.info("[PERF] {}.{}() executed in {} ms", joinPoint.getSignature().getDeclaringType().getSimpleName(), joinPoint.getSignature().getName(), duration);
        }
    }
}
