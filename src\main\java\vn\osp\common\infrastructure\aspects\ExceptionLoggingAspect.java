package vn.osp.common.infrastructure.aspects;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class ExceptionLoggingAspect {
    @Pointcut("execution(public * *(..)) && within(@org.springframework.stereotype.Service *)")
    public void serviceMethods() {
    }

    @Pointcut("execution(public * *(..)) && within(@org.springframework.stereotype.Repository *)")
    public void repositoryMethods() {
    }

    @Pointcut("execution(public * *(..)) && within(@org.springframework.web.bind.annotation.RestController *)")
    public void controllerMethods() {
    }

    /**
     * Match tất cả tầng trong ứng dụng (Controller + Service + Repository)
     */
    @AfterThrowing(pointcut = "serviceMethods() || repositoryMethods() || controllerMethods()", throwing = "ex")
    public void logException(JoinPoint joinPoint, Throwable ex) {
        log.error("[ERROR] Exception in {}.{}() with cause={}", joinPoint.getSignature().getDeclaringType().getSimpleName(), joinPoint.getSignature().getName(), ex.getMessage(), ex);
    }
}
