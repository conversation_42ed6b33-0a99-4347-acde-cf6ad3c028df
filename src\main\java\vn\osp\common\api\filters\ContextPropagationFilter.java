package vn.osp.common.api.filters;

import io.micrometer.tracing.Baggage;
import io.micrometer.tracing.BaggageInScope;
import io.micrometer.tracing.Tracer;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Optional;
import java.util.UUID;

public class ContextPropagationFilter extends OncePerRequestFilter {
    private final Tracer tracer;

    public ContextPropagationFilter(Tracer tracer) {
        this.tracer = tracer;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    Fi<PERSON><PERSON>hain filterChain) throws ServletException, IOException {
        String userId = Optional.ofNullable(this.tracer.getBaggage("userId"))
                .map(Baggage::get)
                .orElse(null);
        String sessionId = Optional.ofNullable(this.tracer.getBaggage("sessionId"))
                .map(Baggage::get)
                .orElse(null);

        if (userId == null || userId.isEmpty()) {
            // Todo: implement spring security
            // String userId = SecurityContextHolder.getContext().getAuthentication().getName();
            userId = UUID.randomUUID().toString();
        }

        if (sessionId == null || sessionId.isEmpty()) {
            HttpSession session = request.getSession(false); // không tạo mới
            sessionId = (session != null) ? session.getId() : null;
        }

        try (BaggageInScope scope1 = this.tracer.createBaggageInScope("userId", userId);
             BaggageInScope scope2 = this.tracer.createBaggageInScope("sessionId", sessionId)) {
            filterChain.doFilter(request, response);
        }
    }
}

